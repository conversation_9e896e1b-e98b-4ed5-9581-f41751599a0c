# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test
*.txt

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build output
build/
dist/
bin/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# API Keys and sensitive files
keys.txt
*.key
*.pem
*.crt
secrets/
config/secrets/

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Gatsby files
.cache/
public

# Temporary folders
tmp/
temp/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# IDE files
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# Compressed files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Local development
local/
dev/

# Test files
test-results/
test-output/

# Profiling files
*.prof
*.pprof

# Air live reload tool
tmp/

# GoLand
.idea/

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# VS Code
*.code-workspace

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Local configuration override
config.local.json
config.local.yaml
config.local.yml

# Certificates
*.crt
*.key
*.pem
*.p12
*.pfx

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Kubernetes
*.kubeconfig

# Helm
charts/*.tgz

# Local development scripts
run-local.sh
start-local.sh
dev-setup.sh
data/
